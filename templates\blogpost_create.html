{% extends 'base.html' %}

{% block content %}
{# Main container with responsive grid #}
<div class="container">
  <div class="row justify-content-center">
    {# Responsive column layout - lg: 4 cols, md: 6 cols, sm: 12 cols #}
    <div class="col-md-6 col-md-6 col-sm-12">
      {# Form header #}
      <h2 class="account-heading">Create a New Blog Post</h2>

      {# Post creation form - Note: enctype required for file uploads #}
      <form method="post" enctype="multipart/form-data" id="blogPostForm">
        {% csrf_token %}

        {# Blog Title Field #}
        <div class="mb-3">
          {{ form.blog_title.label_tag }}
          {{ form.blog_title }}
          <small class="form-text text-muted">
            <span id="blog_title_counter">0</span>/200 characters
          </small>
          <div id="blog_title_error" class="validation-error" style="color: #dd1c1a; font-size: 0.875rem; font-weight: 500; margin-top: 5px; display: none;"></div>
          {% if form.blog_title.errors %}
          <ul class="my_errorlist">
            {% for error in form.blog_title.errors %}
            <li>{{ error }}</li>
            {% endfor %}
          </ul>
          {% endif %}
        </div>

        {# Content Field #}
        <div class="mb-3">
          {{ form.content.label_tag }}
          {{ form.content }}
          <div id="content_error" class="validation-error" style="color: #dd1c1a; font-size: 0.875rem; font-weight: 500; margin-top: 5px; display: none;"></div>
          {% if form.content.errors %}
          <ul class="my_errorlist">
            {% for error in form.content.errors %}
            <li>{{ error }}</li>
            {% endfor %}
          </ul>
          {% endif %}
        </div>

        {# Excerpt Field #}
        <div class="mb-3">
          {{ form.excerpt.label_tag }}
          {{ form.excerpt }}
          <small class="form-text text-muted">
            <span id="excerpt_counter">0</span>/150 characters
          </small>
          <div id="excerpt_error" class="validation-error" style="color: #dd1c1a; font-size: 0.875rem; font-weight: 500; margin-top: 5px; display: none;"></div>
          {% if form.excerpt.errors %}
          <ul class="my_errorlist">
            {% for error in form.excerpt.errors %}
            <li>{{ error }}</li>
            {% endfor %}
          </ul>
          {% endif %}
        </div>

        {# Featured Image Field #}
        <div class="mb-3">
          {{ form.featured_image.label_tag }}
          {{ form.featured_image }}
          {% if form.featured_image.errors %}
          <ul class="my_errorlist">
            {% for error in form.featured_image.errors %}
            <li>{{ error }}</li>
            {% endfor %}
          </ul>
          {% endif %}
        </div>

        {# Media Category Field #}
        <div class="mb-3">
          {{ form.media_category.label_tag }}
          {{ form.media_category }}
          <div id="media_category_error" class="validation-error" style="color: #dd1c1a; font-size: 0.875rem; font-weight: 500; margin-top: 5px; display: none;"></div>
          {% if form.media_category.errors %}
          <ul class="my_errorlist">
            {% for error in form.media_category.errors %}
            <li>{{ error }}</li>
            {% endfor %}
          </ul>
          {% endif %}
        </div>

        {# Release Year Field #}
        <div class="mb-3">
          {{ form.release_year.label_tag }}
          {{ form.release_year }}
          <div id="release_year_error" class="validation-error" style="color: #dd1c1a; font-size: 0.875rem; font-weight: 500; margin-top: 5px; display: none;"></div>
          {% if form.release_year.errors %}
          <ul class="my_errorlist">
            {% for error in form.release_year.errors %}
            <li>{{ error }}</li>
            {% endfor %}
          </ul>
          {% endif %}
        </div>

        {# Media Link Field #}
        <div class="mb-3">
          {{ form.media_link.label_tag }}
          {{ form.media_link }}
          <div id="media_link_error" class="validation-error" style="color: #dd1c1a; font-size: 0.875rem; font-weight: 500; margin-top: 5px; display: none;"></div>
          {% if form.media_link.errors %}
          <ul class="my_errorlist">
            {% for error in form.media_link.errors %}
            <li>{{ error }}</li>
            {% endfor %}
          </ul>
          {% endif %}
        </div>

        <button type="submit" class="btn btn-primary">Create Post</button>
      </form>
    </div>
  </div>
</div>
{% endblock content %}

{% block scripts %}
{{ form.media }}

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Get form elements
    const form = document.getElementById('blogPostForm');
    const blogTitle = document.getElementById('id_blog_title');
    const content = document.getElementById('id_content');
    const excerpt = document.getElementById('id_excerpt');
    const mediaCategory = document.getElementById('id_media_category');
    const releaseYear = document.getElementById('id_release_year');
    const mediaLink = document.getElementById('id_media_link');

    // Validation functions
    function showError(fieldId, message) {
        const errorDiv = document.getElementById(fieldId + '_error');
        const field = document.getElementById('id_' + fieldId);

        if (errorDiv) {
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }

        if (field) {
            field.classList.add('is-invalid');
            field.classList.remove('is-valid');
        }
    }

    function hideError(fieldId) {
        const errorDiv = document.getElementById(fieldId + '_error');
        const field = document.getElementById('id_' + fieldId);

        if (errorDiv) {
            errorDiv.style.display = 'none';
        }

        if (field) {
            field.classList.remove('is-invalid');
            field.classList.add('is-valid');
        }
    }

    function clearValidationState(fieldId) {
        const errorDiv = document.getElementById(fieldId + '_error');
        const field = document.getElementById('id_' + fieldId);

        if (errorDiv) {
            errorDiv.style.display = 'none';
        }

        if (field) {
            field.classList.remove('is-invalid', 'is-valid');
        }
    }

    function updateCharacterCounter(fieldId, maxLength) {
        const field = document.getElementById('id_' + fieldId);
        const counter = document.getElementById(fieldId + '_counter');

        if (field && counter) {
            const currentLength = field.value.length;
            counter.textContent = currentLength;

            // Change color based on usage
            if (currentLength > maxLength * 0.9) {
                counter.style.color = '#dd1c1a'; // Error color
            } else if (currentLength > maxLength * 0.7) {
                counter.style.color = '#f79818'; // Warning color
            } else {
                counter.style.color = '#9198a1'; // Normal color
            }
        }
    }

    function validateBlogTitle() {
        const value = blogTitle.value.trim();
        if (value.length === 0) {
            showError('blog_title', 'Blog title is required.');
            return false;
        } else if (value.length > 200) {
            showError('blog_title', 'Blog title must be 200 characters or less.');
            return false;
        } else {
            hideError('blog_title');
            return true;
        }
    }

    function validateContent() {
        // For Summernote, we need to get the content differently
        let value = '';
        if (typeof $('#id_content').summernote !== 'undefined') {
            value = $('#id_content').summernote('code').replace(/<[^>]*>/g, '').trim();
        } else {
            value = content.value.trim();
        }

        if (value.length === 0) {
            showError('content', 'Content is required.');
            return false;
        } else if (value.length > 10000) {
            showError('content', 'Content must be 10,000 characters or less.');
            return false;
        } else {
            hideError('content');
            return true;
        }
    }

    function validateExcerpt() {
        const value = excerpt.value.trim();
        if (value.length > 150) {
            showError('excerpt', 'Excerpt must be 150 characters or less.');
            return false;
        } else {
            hideError('excerpt');
            return true;
        }
    }

    function validateMediaCategory() {
        if (!mediaCategory.value || mediaCategory.value === '') {
            showError('media_category', 'Please select a media category.');
            return false;
        } else {
            hideError('media_category');
            return true;
        }
    }

    function validateReleaseYear() {
        const value = releaseYear.value.trim();
        const currentYear = new Date().getFullYear();

        if (value === '') {
            showError('release_year', 'Release year is required.');
            return false;
        }

        const year = parseInt(value);
        if (isNaN(year)) {
            showError('release_year', 'Please enter a valid year.');
            return false;
        }

        if (year < 1800 || year > currentYear) {
            showError('release_year', `Please enter a year between 1800 and ${currentYear}.`);
            return false;
        }

        hideError('release_year');
        return true;
    }

    function validateMediaLink() {
        const value = mediaLink.value.trim();

        if (value === '' || value === 'http://www.') {
            showError('media_link', 'Media link is required.');
            return false;
        }

        // Basic URL validation
        const urlPattern = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/;
        if (!urlPattern.test(value)) {
            showError('media_link', 'Please enter a valid URL.');
            return false;
        }

        hideError('media_link');
        return true;
    }

    // Initialize character counters
    if (blogTitle) {
        updateCharacterCounter('blog_title', 200);
    }
    if (excerpt) {
        updateCharacterCounter('excerpt', 150);
    }

    // Add real-time validation listeners
    if (blogTitle) {
        blogTitle.addEventListener('focus', function() {
            clearValidationState('blog_title');
        });
        blogTitle.addEventListener('blur', validateBlogTitle);
        blogTitle.addEventListener('input', function() {
            updateCharacterCounter('blog_title', 200);
            if (this.value.trim().length > 0) {
                validateBlogTitle();
            } else {
                clearValidationState('blog_title');
            }
        });
    }

    if (excerpt) {
        excerpt.addEventListener('focus', function() {
            clearValidationState('excerpt');
        });
        excerpt.addEventListener('blur', validateExcerpt);
        excerpt.addEventListener('input', function() {
            updateCharacterCounter('excerpt', 150);
            validateExcerpt();
        });
    }

    if (mediaCategory) {
        mediaCategory.addEventListener('focus', function() {
            clearValidationState('media_category');
        });
        mediaCategory.addEventListener('change', validateMediaCategory);
    }

    if (releaseYear) {
        releaseYear.addEventListener('focus', function() {
            clearValidationState('release_year');
        });
        releaseYear.addEventListener('blur', validateReleaseYear);
        releaseYear.addEventListener('input', function() {
            if (this.value.trim().length > 0) {
                validateReleaseYear();
            } else {
                clearValidationState('release_year');
            }
        });
    }

    if (mediaLink) {
        mediaLink.addEventListener('focus', function() {
            clearValidationState('media_link');
        });
        mediaLink.addEventListener('blur', validateMediaLink);
        mediaLink.addEventListener('input', function() {
            if (this.value.trim().length > 0 && this.value.trim() !== 'http://www.') {
                validateMediaLink();
            } else if (this.value.trim().length === 0 || this.value.trim() === 'http://www.') {
                clearValidationState('media_link');
            }
        });
    }

    // Form submission validation
    form.addEventListener('submit', function(e) {
        let isValid = true;

        isValid = validateBlogTitle() && isValid;
        isValid = validateContent() && isValid;
        isValid = validateExcerpt() && isValid;
        isValid = validateMediaCategory() && isValid;
        isValid = validateReleaseYear() && isValid;
        isValid = validateMediaLink() && isValid;

        if (!isValid) {
            e.preventDefault();
            // Scroll to first error
            const firstError = document.querySelector('.validation-error[style*="block"]');
            if (firstError) {
                firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }
    });
});
</script>
{% endblock scripts %}
